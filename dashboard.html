<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroManager - Dashboard</title>

    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <link rel="manifest" href="./manifest.json">

    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <script src="https://unpkg.com/lucide@latest"></script>

    <script src="./pwa-utils.js"></script>

    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --success-50: #f0fdf4;
            --success-500: #22c55e;
            --warning-50: #fffbeb;
            --warning-500: #f59e0b;
            --error-50: #fef2f2;
            --error-500: #ef4444;
        }

        /* Custom base styles */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; }
        ::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 10px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced sidebar styles */
        .sidebar-item {
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .sidebar-item.active .sidebar-link-text,
        .sidebar-item.active i {
            color: white !important;
        }

        .sidebar-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #ffffff;
            border-radius: 0 4px 4px 0;
        }

        /* Sidebar transition and base styles */
        #sidebar {
            transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Collapsed state for the sidebar */
        #sidebar.collapsed {
            width: 5rem; /* w-20 */
        }

        #sidebar.collapsed .sidebar-link {
            justify-content: center;
        }

        #sidebar.collapsed .sidebar-link > i {
            margin-right: 0;
        }

        #sidebar.collapsed .sidebar-link-text,
        #sidebar.collapsed .logo-text,
        #sidebar.collapsed .sidebar-badge {
            display: none;
        }

        #sidebar.collapsed #sidebar-toggle .lucide-chevrons-left {
            transform: rotate(180deg);
        }
        
        /* Tooltip for collapsed sidebar */
        .sidebar-item .tooltip {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s;
            position: absolute;
            left: 5.5rem; /* Position next to the icon */
            background-color: #1f2937;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 50;
        }

        #sidebar.collapsed .sidebar-item:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }

        /* Active link styles */
        .sidebar-link.active {
            background-color: #eff6ff;
            color: #2563eb;
            font-weight: 600;
        }
        .sidebar-link.active svg { color: #ffffff; }

        /* Content section visibility */
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }

        /* Active filter button style */
        .filter-btn.active {
            background-color: #2563eb;
            color: white;
        }
        
        /* Drag and Drop styles */
        .task-card.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
        }
        .kanban-column .tasks-container.drag-over {
            border-style: dashed;
            border-color: #2563eb;
            background-color: #eff6ff;
        }

        /* Professional utility styles */
        .metric-trend {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 12px;
        }

        .trend-up {
            background: var(--success-50);
            color: var(--success-500);
        }

        .trend-down {
            background: var(--error-50);
            color: var(--error-500);
        }

        .trend-neutral {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: var(--success-50);
            color: var(--success-500);
        }

        .status-pending {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-completed {
            background: var(--primary-50);
            color: var(--primary-600);
        }

        .quick-action {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .quick-action:hover {
            border-color: var(--primary-500);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .table-row {
            transition: all 0.2s ease;
        }

        .table-row:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: scale(1.01);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 1024px) {
            #sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
            #sidebar.open {
                transform: translateX(0);
            }
            #main-content-wrapper {
                margin-left: 0;
            }
        }

        /* Professional Chart Styles */
        .chart-container {
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            overflow: hidden;
        }

        .chart-bar {
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: bottom;
            animation: barGrow 1.2s ease-out;
        }

        .chart-bar:hover {
            transform: scaleY(1.05);
            filter: brightness(1.1);
        }

        @keyframes barGrow {
            from {
                transform: scaleY(0);
            }
            to {
                transform: scaleY(1);
            }
        }

        .chart-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 2s ease-out forwards;
        }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        .chart-point {
            opacity: 0;
            animation: fadeInPoint 0.5s ease-out forwards;
        }

        .chart-point:nth-child(1) { animation-delay: 0.2s; }
        .chart-point:nth-child(2) { animation-delay: 0.4s; }
        .chart-point:nth-child(3) { animation-delay: 0.6s; }
        .chart-point:nth-child(4) { animation-delay: 0.8s; }
        .chart-point:nth-child(5) { animation-delay: 1.0s; }
        .chart-point:nth-child(6) { animation-delay: 1.2s; }
        .chart-point:nth-child(7) { animation-delay: 1.4s; }
        .chart-point:nth-child(8) { animation-delay: 1.6s; }
        .chart-point:nth-child(9) { animation-delay: 1.8s; }
        .chart-point:nth-child(10) { animation-delay: 2.0s; }

        @keyframes fadeInPoint {
            to {
                opacity: 1;
            }
        }

        .pie-segment {
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .pie-segment:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        .chart-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .chart-grid {
            opacity: 0.3;
        }

        .chart-axis {
            stroke: #94a3b8;
            stroke-width: 1;
        }

        .chart-label {
            font-size: 11px;
            fill: #64748b;
            font-weight: 500;
        }

        .donut-segment {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
            cursor: pointer;
        }

        .donut-segment:hover {
            transform: scale(1.08);
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
        }

        .chart-legend-item {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .chart-legend-item:hover {
            transform: translateX(4px);
            background: rgba(59, 130, 246, 0.05);
        }

        @keyframes drawPie {
            from {
                stroke-dasharray: 0 502;
            }
        }

    </style>
</head>
<body class="bg-slate-100">

    <div class="relative h-screen overflow-hidden lg:flex">
        <div id="sidebar-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden lg:hidden"></div>

        <aside id="sidebar" class="w-64 bg-white shadow-lg flex flex-col fixed inset-y-0 left-0 transform lg:relative z-30">
            <div class="h-20 flex items-center justify-center border-b px-4 overflow-hidden flex-shrink-0">
                <a href="#" class="flex items-center gap-2">
                    <i data-lucide="chef-hat" class="w-8 h-8 text-blue-600 flex-shrink-0"></i>
                    <h1 class="text-2xl font-bold text-blue-600 logo-text whitespace-nowrap">Restro<span class="text-slate-800">Manager</span></h1>
                </a>
            </div>
            
            <nav class="flex-1 mt-6 space-y-1 px-3 overflow-y-auto overflow-x-hidden">
                <a href="#" data-target="dashboard" class="sidebar-item sidebar-link active flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Dashboard</span>
                    <span class="tooltip">Dashboard</span>
                </a>
                <a href="#" data-target="orders" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="shopping-cart" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Orders</span>
                    <span class="sidebar-badge ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">8</span>
                    <span class="tooltip">Orders</span>
                </a>
                <a href="#" data-target="tables" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="layout" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Tables</span>
                    <span class="sidebar-badge ml-auto bg-green-100 text-green-600 text-xs font-medium px-2 py-1 rounded-full">12/16</span>
                    <span class="tooltip">Tables</span>
                </a>
                <a href="#" data-target="tasks" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="clipboard-check" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Tasks</span>
                    <span class="sidebar-badge ml-auto bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">5</span>
                    <span class="tooltip">Tasks</span>
                </a>
                <a href="#" data-target="customers" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="users" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Customers</span>
                    <span class="tooltip">Customers</span>
                </a>
                <a href="#" data-target="inventory" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="package" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Inventory</span>
                    <span class="sidebar-badge ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">!</span>
                    <span class="tooltip">Inventory</span>
                </a>
                <a href="#" data-target="pos" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="credit-card" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">POS System</span>
                    <span class="tooltip">POS System</span>
                </a>
                <a href="#" data-target="analytics" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="bar-chart-2" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Analytics</span>
                    <span class="tooltip">Analytics</span>
                </a>
            </nav>
            
            <div class="flex-shrink-0">
                <div class="mb-2 px-3">
                    <a href="#" data-target="settings" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="settings" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Settings</span>
                         <span class="tooltip">Settings</span>
                    </a>
                    <button id="sidebar-toggle" class="hidden lg:flex items-center justify-center w-full py-3 text-slate-500 hover:bg-slate-100 rounded-lg mt-2">
                        <i data-lucide="chevrons-left" class="w-5 h-5 transition-transform duration-300"></i>
                    </button>
                </div>
            </div>
        </aside>

        <div id="main-content-wrapper" class="flex-1 flex flex-col overflow-x-hidden">
            <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                <div class="flex items-center">
                    <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Dashboard</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                        <span id="role-icon">👨‍💼</span>
                        <span id="role-text">Admin</span>
                    </div>

                    <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                    <div class="relative">
                        <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                            <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                            <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                            <i data-lucide="chevron-down" class="w-4 h-4"></i>
                        </button>

                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                            <div class="px-4 py-2 border-b border-slate-100">
                                <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="user" class="w-4 h-4"></i>
                                Profile
                            </a>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="settings" class="w-4 h-4"></i>
                                Settings
                            </a>
                            <hr class="my-2">
                            <button id="logout-btn" class="flex items-center gap-2 w-full px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                <i data-lucide="log-out" class="w-4 h-4"></i>
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-slate-50 to-blue-50 p-4 sm:p-6 lg:p-8">
                <div id="welcome-banner" class="hidden mb-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold mb-2">🎉 Welcome to RestroManager!</h3>
                            <p class="text-blue-100">Hi <span id="user-name-banner"></span>! Your account has been successfully created. Let's get your restaurant set up!</p>
                        </div>
                        <button id="dismiss-welcome" class="text-blue-200 hover:text-white transition-colors">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                    <div class="mt-4 flex flex-wrap gap-3">
                        <button class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                            Complete Setup
                        </button>
                        <button class="bg-blue-400 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-300 transition-colors">
                            Watch Tutorial
                        </button>
                    </div>
                </div>

                <div id="dashboard-content" class="content-section active">
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.1s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Today's Revenue</span>
                                    <span class="text-2xl font-bold text-slate-800">₹24,589</span>
                                </div>
                                <div class="p-2 bg-blue-50 rounded-lg">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>12.5%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.2s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Active Orders</span>
                                    <span class="text-2xl font-bold text-slate-800">18</span>
                                </div>
                                <div class="p-2 bg-green-50 rounded-lg">
                                    <i data-lucide="clipboard-list" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>8.2%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last hour</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.3s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Table Occupancy</span>
                                    <span class="text-2xl font-bold text-slate-800">76%</span>
                                </div>
                                <div class="p-2 bg-orange-50 rounded-lg">
                                    <i data-lucide="utensils" class="w-6 h-6 text-orange-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-neutral">
                                    <i data-lucide="minus" class="w-3 h-3"></i>
                                    <span>2.1%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.4s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Customer Rating</span>
                                    <span class="text-2xl font-bold text-slate-800">4.8/5</span>
                                </div>
                                <div class="p-2 bg-purple-50 rounded-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>0.3</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last week</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="lg:col-span-2 glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.5s;">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Revenue Analytics</h3>
                                    <p class="text-sm text-slate-500">Last 7 days performance</p>
                                </div>
                                <div class="flex gap-2">
                                    <button class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-lg">7D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">30D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">90D</button>
                                </div>
                            </div>
                            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                                <div class="text-center">
                                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-blue-400 mx-auto mb-2"></i>
                                    <p class="text-sm text-slate-500">Revenue chart will be displayed here</p>
                                </div>
                            </div>
                        </div>

                        <div class="glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.6s;">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Top Dishes</h3>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍕</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Margherita Pizza</p>
                                            <p class="text-xs text-slate-500">42 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-green-600">₹1,680</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍝</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Pasta Alfredo</p>
                                            <p class="text-xs text-slate-500">38 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-blue-600">₹1,520</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍔</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Classic Burger</p>
                                            <p class="text-xs text-slate-500">35 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-orange-600">₹1,400</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🥗</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Caesar Salad</p>
                                            <p class="text-xs text-slate-500">28 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-purple-600">₹1,120</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div id="orders-content" class="content-section">
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-blue-100 rounded-xl">
                                    <i data-lucide="receipt" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Orders Management</h1>
                                    <p class="text-slate-600 text-sm">Real-time order tracking and management</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Live updates
                                </span>
                                <span>Last updated: Just now</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg shadow-blue-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                New Order
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="shopping-cart" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Orders Today</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">247</p>
                                <p class="text-xs text-slate-600">vs 221 yesterday</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-orange-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                        Urgent
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Pending Orders</p>
                                <p class="text-3xl font-bold text-orange-600 mb-1">8</p>
                                <p class="text-xs text-slate-600">Awaiting confirmation</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                        Active
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">In Kitchen</p>
                                <p class="text-3xl font-bold text-purple-600 mb-1">15</p>
                                <p class="text-xs text-slate-600">Being prepared</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-green-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Completed Today</p>
                                <p class="text-3xl font-bold text-green-600 mb-1">224</p>
                                <p class="text-xs text-slate-600">₹18,450 revenue</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-slate-200 mb-6">
                        <div class="mb-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                <input type="text" id="order-search" placeholder="Search orders..."
                                       class="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button class="order-filter-btn active px-3 py-1.5 bg-blue-600 text-white rounded-lg text-xs font-medium" data-status="all">
                                All (247)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-orange-100 text-orange-700 rounded-lg text-xs font-medium hover:bg-orange-200" data-status="pending">
                                Pending (8)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-purple-100 text-purple-700 rounded-lg text-xs font-medium hover:bg-purple-200" data-status="preparing">
                                Kitchen (15)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium hover:bg-yellow-200" data-status="ready">
                                Ready (5)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-green-100 text-green-700 rounded-lg text-xs font-medium hover:bg-green-200" data-status="completed">
                                Done (224)
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="px-4 py-3 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-700">Recent Orders</h3>
                                <span class="text-xs text-slate-500">10 of 247</span>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Order</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Customer</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden sm:table-cell">Table</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Items</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Total</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Status</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden lg:table-cell">Time</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table-body" class="divide-y divide-slate-100">
                                    <tr class="hover:bg-slate-50 transition-colors" data-status="pending">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-001</div>
                                            <div class="text-xs text-slate-500">2 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">John Smith</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 5</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">2x Burger, 1x Fries</div>
                                            <div class="text-xs text-blue-600">+1 more</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹850</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                Pending
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:34 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                    Accept
                                                </button>
                                                <button class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
                                                    Reject
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="preparing">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-002</div>
                                            <div class="text-xs text-slate-500">8 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Sarah Johnson</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 3</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pizza, 2x Coke</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹1,200</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                Kitchen
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:26 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                                Mark Ready
                                            </button>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="ready">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-003</div>
                                            <div class="text-xs text-slate-500">12 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Mike Davis</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 7</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pasta, 1x Salad</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹950</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Ready
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:22 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                Serve
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="bg-slate-50 px-4 py-3 border-t border-slate-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 1-10 of 247 orders
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white" disabled>
                                        Previous
                                    </button>
                                    <button class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded">1</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">2</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">3</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tasks-content" class="content-section">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="kanban-square" class="w-5 h-5 text-white"></i>
                                </div>
                                <div>
                                    <h1 class="text-xl font-bold text-slate-900 mb-1">Task Management</h1>
                                    <p class="text-sm text-slate-500">Organize, assign, and track team tasks efficiently</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-3">
                                <button id="add-task-btn" class="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium text-sm">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    Add Task
                                </button>
                                <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                    <i data-lucide="filter" class="w-4 h-4"></i>
                                    Filter
                                </button>
                                <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                    <i data-lucide="users" class="w-4 h-4"></i>
                                    Team
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center gap-6 mt-4 pt-4 border-t border-slate-100">
                            <div class="flex items-center gap-2 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-slate-600">Real-time updates</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <span class="font-medium text-slate-900">5</span>
                                <span class="text-slate-600">active tasks</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <span class="font-medium text-slate-900">3</span>
                                <span class="text-slate-600">team members</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="list-todo" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">+2 today</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Tasks</p>
                                <p class="text-3xl font-bold text-slate-900">12</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">Pending</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">To Do</p>
                                <p class="text-3xl font-bold text-orange-600">5</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="play-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">Active</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">In Progress</p>
                                <p class="text-3xl font-bold text-purple-600">3</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">+4 today</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Completed</p>
                                <p class="text-3xl font-bold text-green-600">4</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div id="all-tasks" class="tasks-container space-y-3">
                            <div id="task-1" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                        <span class="text-xs font-semibold text-red-600 bg-red-50 px-2.5 py-1 rounded-md border border-red-200">HIGH</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Clean the main dining area</h5>
                                        <p class="text-sm text-slate-600 line-clamp-1">Wipe down all tables, chairs, and sanitize surfaces before opening.</p>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/FEE2E2/B91C1C?text=SM" alt="Sarah Miller">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>30m</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-md border border-blue-200">Cleaning</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-2" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                        <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Restock bar supplies</h5>
                                        <p class="text-sm text-slate-600 line-clamp-1">Check inventory for lemons, limes, and other bar essentials.</p>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/D1FAE5/047857?text=KW" alt="Kevin Wang">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>45m</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 bg-indigo-50 rounded-md border border-indigo-200">Inventory</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-3" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="inprogress">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-purple-500 rounded-full animate-pulse"></div>
                                        <span class="text-xs font-semibold text-purple-600 bg-purple-50 px-2.5 py-1 rounded-md border border-purple-200">IN PROGRESS</span>
                                        <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Prepare daily specials</h5>
                                        <div class="flex items-center gap-3">
                                            <p class="text-sm text-slate-600 line-clamp-1 flex-1">Prep ingredients for the Chef's special pasta dish.</p>
                                            <div class="flex items-center gap-2">
                                                <div class="w-20 bg-slate-200 rounded-full h-1.5">
                                                    <div class="bg-purple-500 h-1.5 rounded-full transition-all duration-300" style="width: 65%"></div>
                                                </div>
                                                <span class="text-xs text-purple-600 font-semibold">65%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/EEF2FF/4338CA?text=EB" alt="Emily Brown">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>20m left</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-amber-700 bg-amber-50 rounded-md border border-amber-200">Kitchen</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-4" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="done">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-green-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">DONE</span>
                                        <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">LOW</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Set up outdoor seating</h5>
                                        <div class="flex items-center gap-3">
                                            <p class="text-sm text-slate-600 line-clamp-1 flex-1">Arrange tables and umbrellas on the patio.</p>
                                            <div class="flex items-center gap-2 text-xs">
                                                <i data-lucide="check-circle" class="w-3 h-3 text-green-600"></i>
                                                <span class="text-green-700 font-medium">2h ago</span>
                                                <span class="text-slate-300">•</span>
                                                <span class="text-green-600 font-medium">On time</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex items-center gap-2">
                                            <div class="flex -space-x-1">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                            </div>
                                            <span class="text-xs text-slate-500 font-medium">John</span>
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-50 rounded-md border border-green-200">Setup</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button class="w-full p-4 border-2 border-dashed border-slate-300 rounded-lg text-slate-500 hover:border-purple-300 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200 flex items-center justify-center gap-2">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                Add new task
                            </button>
                        </div>
                    </div>
                </div>

                <div id="tables-content" class="content-section">
                    <!-- Table Management Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-emerald-100 rounded-xl">
                                    <i data-lucide="layout" class="w-6 h-6 text-emerald-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Table Management</h1>
                                    <p class="text-slate-600 text-sm">Real-time table status and reservation management</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Live updates
                                </span>
                                <span>16 total tables</span>
                                <span class="flex items-center gap-1">
                                    <i data-lucide="users" class="w-4 h-4 text-emerald-500"></i>
                                    12 occupied • 4 available
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white rounded-xl hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 font-medium shadow-lg shadow-emerald-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                New Reservation
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="layout-grid" class="w-4 h-4"></i>
                                Floor Plan
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="calendar" class="w-4 h-4"></i>
                                Schedule
                            </button>
                        </div>
                    </div>

                    <!-- Table Status Overview -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-emerald-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                        Available
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Available Tables</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">4</p>
                                <p class="text-xs text-slate-600">Ready for seating</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-blue-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                        Active
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Occupied Tables</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">12</p>
                                <p class="text-xs text-slate-600">Currently dining</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        Today
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Reservations</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">18</p>
                                <p class="text-xs text-slate-600">Scheduled today</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="percent" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8.5%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Occupancy Rate</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">75%</p>
                                <p class="text-xs text-slate-600">Current utilization</p>
                            </div>
                        </div>
                    </div>

                    <!-- Interactive Floor Plan -->
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                    <i data-lucide="layout-grid" class="w-5 h-5 text-emerald-600"></i>
                                    Interactive Floor Plan
                                </h3>
                                <p class="text-sm text-slate-500">Real-time table status • Click tables for details</p>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="flex items-center gap-4 text-sm">
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-emerald-500 rounded"></div>
                                        <span class="text-slate-600">Available</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                        <span class="text-slate-600">Occupied</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-orange-500 rounded"></div>
                                        <span class="text-slate-600">Reserved</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-red-500 rounded"></div>
                                        <span class="text-slate-600">Cleaning</span>
                                    </div>
                                </div>
                                <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">Fullscreen</button>
                            </div>
                        </div>

                        <!-- Floor Plan Grid -->
                        <div class="relative bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl p-8 min-h-96">
                            <!-- Restaurant Layout -->
                            <div class="grid grid-cols-4 gap-6 h-full">
                                <!-- Left Side Tables -->
                                <div class="space-y-6">
                                    <!-- Table 1 - Available -->
                                    <div class="table-item bg-emerald-100 border-2 border-emerald-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="1" data-status="available">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-emerald-800">Table 1</span>
                                            <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
                                        </div>
                                        <div class="text-xs text-emerald-700">
                                            <p>2 Seats • Available</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Click to reserve</p>
                                        </div>
                                    </div>

                                    <!-- Table 2 - Occupied -->
                                    <div class="table-item bg-blue-100 border-2 border-blue-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="2" data-status="occupied">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-blue-800">Table 2</span>
                                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                        </div>
                                        <div class="text-xs text-blue-700">
                                            <p>4 Seats • Occupied</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Since 7:30 PM</p>
                                        </div>
                                    </div>

                                    <!-- Table 3 - Reserved -->
                                    <div class="table-item bg-orange-100 border-2 border-orange-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="3" data-status="reserved">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-orange-800">Table 3</span>
                                            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                        </div>
                                        <div class="text-xs text-orange-700">
                                            <p>6 Seats • Reserved</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">8:00 PM - Sharma</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Center Area -->
                                <div class="col-span-2 flex flex-col justify-center items-center space-y-6">
                                    <!-- Kitchen/Bar Area -->
                                    <div class="w-full bg-slate-200 rounded-xl p-6 text-center">
                                        <i data-lucide="chef-hat" class="w-8 h-8 text-slate-600 mx-auto mb-2"></i>
                                        <p class="text-sm font-medium text-slate-700">Kitchen Area</p>
                                    </div>

                                    <!-- Center Tables -->
                                    <div class="grid grid-cols-2 gap-4 w-full">
                                        <!-- Table 8 - Occupied -->
                                        <div class="table-item bg-blue-100 border-2 border-blue-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="8" data-status="occupied">
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="font-bold text-blue-800">Table 8</span>
                                                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                            </div>
                                            <div class="text-xs text-blue-700">
                                                <p>4 Seats • Occupied</p>
                                                <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Since 6:45 PM</p>
                                            </div>
                                        </div>

                                        <!-- Table 9 - Available -->
                                        <div class="table-item bg-emerald-100 border-2 border-emerald-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="9" data-status="available">
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="font-bold text-emerald-800">Table 9</span>
                                                <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
                                            </div>
                                            <div class="text-xs text-emerald-700">
                                                <p>2 Seats • Available</p>
                                                <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Click to reserve</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Bar Area -->
                                    <div class="w-full bg-slate-200 rounded-xl p-4 text-center">
                                        <i data-lucide="wine" class="w-6 h-6 text-slate-600 mx-auto mb-1"></i>
                                        <p class="text-xs font-medium text-slate-700">Bar Counter</p>
                                    </div>
                                </div>

                                <!-- Right Side Tables -->
                                <div class="space-y-6">
                                    <!-- Table 4 - Occupied -->
                                    <div class="table-item bg-blue-100 border-2 border-blue-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="4" data-status="occupied">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-blue-800">Table 4</span>
                                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                        </div>
                                        <div class="text-xs text-blue-700">
                                            <p>4 Seats • Occupied</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Since 7:15 PM</p>
                                        </div>
                                    </div>

                                    <!-- Table 5 - Cleaning -->
                                    <div class="table-item bg-red-100 border-2 border-red-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="5" data-status="cleaning">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-red-800">Table 5</span>
                                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        </div>
                                        <div class="text-xs text-red-700">
                                            <p>6 Seats • Cleaning</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">ETA: 5 minutes</p>
                                        </div>
                                    </div>

                                    <!-- Table 6 - Available -->
                                    <div class="table-item bg-emerald-100 border-2 border-emerald-300 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-200 group" data-table="6" data-status="available">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="font-bold text-emerald-800">Table 6</span>
                                            <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
                                        </div>
                                        <div class="text-xs text-emerald-700">
                                            <p>8 Seats • Available</p>
                                            <p class="mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Large party ready</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Entrance -->
                            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-slate-300 rounded-lg px-4 py-2">
                                <div class="flex items-center gap-2">
                                    <i data-lucide="door-open" class="w-4 h-4 text-slate-600"></i>
                                    <span class="text-sm font-medium text-slate-700">Entrance</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reservations & Table Details -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Today's Reservations -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <i data-lucide="calendar-check" class="w-5 h-5 text-emerald-600"></i>
                                        Today's Reservations
                                    </h3>
                                    <p class="text-sm text-slate-500">18 reservations scheduled</p>
                                </div>
                                <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center gap-1">
                                    View All
                                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                                </button>
                            </div>

                            <div class="space-y-4">
                                <!-- Upcoming Reservation -->
                                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl border border-orange-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="clock" class="w-5 h-5 text-orange-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-slate-900">Sharma Family</p>
                                            <p class="text-sm text-slate-600">Table 3 • 6 guests</p>
                                            <p class="text-xs text-orange-600 font-medium">Next: 8:00 PM</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <button class="px-3 py-1.5 bg-orange-600 text-white text-xs rounded-lg hover:bg-orange-700 transition-colors">
                                            Check In
                                        </button>
                                        <p class="text-xs text-slate-500 mt-1">+91 98765 43210</p>
                                    </div>
                                </div>

                                <!-- Current Reservations -->
                                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="users" class="w-5 h-5 text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-slate-900">Kumar Party</p>
                                            <p class="text-sm text-slate-600">Table 2 • 4 guests</p>
                                            <p class="text-xs text-blue-600 font-medium">Dining since 7:30 PM</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Active
                                        </span>
                                        <p class="text-xs text-slate-500 mt-1">Order #ORD-142</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="calendar" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-slate-900">Patel Anniversary</p>
                                            <p class="text-sm text-slate-600">Table 6 • 8 guests</p>
                                            <p class="text-xs text-green-600 font-medium">9:00 PM reservation</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Confirmed
                                        </span>
                                        <p class="text-xs text-slate-500 mt-1">Special occasion</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="heart" class="w-5 h-5 text-purple-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-slate-900">Date Night - Wilson</p>
                                            <p class="text-sm text-slate-600">Table 1 • 2 guests</p>
                                            <p class="text-xs text-purple-600 font-medium">9:30 PM reservation</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            VIP
                                        </span>
                                        <p class="text-xs text-slate-500 mt-1">Window table</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Table Performance Analytics -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <i data-lucide="bar-chart-3" class="w-5 h-5 text-emerald-600"></i>
                                        Table Performance
                                    </h3>
                                    <p class="text-sm text-slate-500">Today's utilization metrics</p>
                                </div>
                                <button class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">Details</button>
                            </div>

                            <div class="space-y-4">
                                <!-- Top Performing Table -->
                                <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-green-900">Best Performer</h4>
                                        <span class="text-lg font-bold text-green-600">Table 4</span>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <p class="text-green-700">Turnover Rate</p>
                                            <p class="font-bold text-green-900">4.2 times</p>
                                        </div>
                                        <div>
                                            <p class="text-green-700">Revenue</p>
                                            <p class="font-bold text-green-900">₹3,240</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Average Metrics -->
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="clock" class="w-3 h-3 text-blue-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Avg. Dining Time</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">1h 25m</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="rotate-cw" class="w-3 h-3 text-purple-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Avg. Turnover</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">3.1 times</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="dollar-sign" class="w-3 h-3 text-orange-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Revenue per Table</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">₹1,890</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="trending-up" class="w-3 h-3 text-emerald-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Peak Hour Efficiency</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">92%</span>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="pt-4 border-t border-slate-100">
                                    <div class="grid grid-cols-2 gap-3">
                                        <button class="flex items-center justify-center gap-2 p-3 bg-emerald-100 text-emerald-700 rounded-lg hover:bg-emerald-200 transition-colors text-sm font-medium">
                                            <i data-lucide="plus" class="w-4 h-4"></i>
                                            Quick Reserve
                                        </button>
                                        <button class="flex items-center justify-center gap-2 p-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                                            <i data-lucide="layout" class="w-4 h-4"></i>
                                            Rearrange
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table Status List -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Table Status Overview</h3>
                                    <p class="text-sm text-slate-500">Real-time status of all 16 tables</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                        Refresh
                                    </button>
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        Configure
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-6 font-semibold text-slate-700 text-sm">Table</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Capacity</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Current Guest</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Duration</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Order Value</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Next Reservation</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-slate-100">
                                    <!-- Available Table -->
                                    <tr class="hover:bg-emerald-50 transition-colors">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                                                    <span class="font-bold text-emerald-600">1</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Table 1</p>
                                                    <p class="text-xs text-slate-500">Window seat</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">2 guests</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                                <div class="w-2 h-2 bg-emerald-500 rounded-full mr-1"></div>
                                                Available
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600">Wilson (9:30 PM)</p>
                                                <p class="text-xs text-slate-500">VIP • 2 guests</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <button class="px-3 py-1.5 bg-emerald-600 text-white text-xs rounded hover:bg-emerald-700 transition-colors">
                                                Reserve
                                            </button>
                                        </td>
                                    </tr>

                                    <!-- Occupied Table -->
                                    <tr class="hover:bg-blue-50 transition-colors">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <span class="font-bold text-blue-600">2</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Table 2</p>
                                                    <p class="text-xs text-slate-500">Center area</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">4 guests</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse"></div>
                                                Occupied
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm font-medium text-slate-900">Kumar Party</p>
                                                <p class="text-xs text-slate-500">4 guests</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">45 min</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-green-600">₹1,240</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">None</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded hover:bg-blue-200 transition-colors">
                                                    Bill
                                                </button>
                                                <button class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Order
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Reserved Table -->
                                    <tr class="hover:bg-orange-50 transition-colors">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                                    <span class="font-bold text-orange-600">3</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Table 3</p>
                                                    <p class="text-xs text-slate-500">Corner booth</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">6 guests</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-1"></div>
                                                Reserved
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm font-medium text-slate-900">Sharma Family</p>
                                                <p class="text-xs text-slate-500">6 guests</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-orange-600">Arriving 8:00 PM</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">Current</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <button class="px-3 py-1.5 bg-orange-600 text-white text-xs rounded hover:bg-orange-700 transition-colors">
                                                Check In
                                            </button>
                                        </td>
                                    </tr>

                                    <!-- Cleaning Table -->
                                    <tr class="hover:bg-red-50 transition-colors">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                                    <span class="font-bold text-red-600">5</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Table 5</p>
                                                    <p class="text-xs text-slate-500">Large table</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">6 guests</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <div class="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                                                Cleaning
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-red-600">ETA: 5 min</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">-</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-400">None</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <button class="px-3 py-1.5 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors">
                                                Mark Clean
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Table Footer -->
                        <div class="px-6 py-4 border-t border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 4 of 16 tables • 75% occupancy rate
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Previous
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium bg-emerald-600 text-white rounded-lg">
                                        1
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        2
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="pos-content" class="content-section">
                    <!-- POS System Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-indigo-100 rounded-xl">
                                    <i data-lucide="credit-card" class="w-6 h-6 text-indigo-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Point of Sale System</h1>
                                    <p class="text-slate-600 text-sm">Complete order management and payment processing</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    System online
                                </span>
                                <span>Table 5 selected</span>
                                <span class="flex items-center gap-1">
                                    <i data-lucide="clock" class="w-4 h-4 text-indigo-500"></i>
                                    Order #ORD-156 in progress
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 font-medium shadow-lg shadow-indigo-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                New Order
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="receipt" class="w-4 h-4"></i>
                                Print Receipt
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="history" class="w-4 h-4"></i>
                                Order History
                            </button>
                        </div>
                    </div>

                    <!-- POS Main Interface -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Menu Categories & Items -->
                        <div class="lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Menu Items</h3>
                                <div class="w-full sm:w-auto">
                                    <div class="relative">
                                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                        <input type="text" placeholder="Search menu..." class="w-full sm:w-64 pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    </div>
                                </div>
                            </div>

                            <!-- Category Tabs -->
                            <div class="flex gap-2 mb-6 overflow-x-auto pb-2">
                                <button class="menu-category-btn active px-4 py-2.5 bg-indigo-600 text-white rounded-lg text-sm font-medium whitespace-nowrap shadow-sm" data-category="all">
                                    All Items
                                </button>
                                <button class="menu-category-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200 whitespace-nowrap transition-colors" data-category="appetizers">
                                    Appetizers
                                </button>
                                <button class="menu-category-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200 whitespace-nowrap transition-colors" data-category="mains">
                                    Mains
                                </button>
                                <button class="menu-category-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200 whitespace-nowrap transition-colors" data-category="beverages">
                                    Beverages
                                </button>
                                <button class="menu-category-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200 whitespace-nowrap transition-colors" data-category="desserts">
                                    Desserts
                                </button>
                            </div>

                            <!-- Menu Items Grid -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 max-h-96 overflow-y-auto pr-2">
                                <!-- Popular Items -->
                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="margherita-pizza" data-price="450">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-red-100 to-red-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍕</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Margherita Pizza</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Classic tomato & mozzarella cheese</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹450</span>
                                                        <span class="px-1.5 py-0.5 bg-green-100 text-green-700 text-xs font-medium rounded-full">Popular</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="pasta-alfredo" data-price="380">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-100 to-orange-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍝</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Pasta Alfredo</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Creamy white sauce pasta</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹380</span>
                                                        <span class="px-1.5 py-0.5 bg-orange-100 text-orange-700 text-xs font-medium rounded-full">Chef's Special</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="grilled-chicken" data-price="520">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍗</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Grilled Chicken</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Herb marinated tender chicken</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹520</span>
                                                        <span class="px-1.5 py-0.5 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">Premium</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="appetizers" data-item="spring-rolls" data-price="180">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-amber-100 to-yellow-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🥟</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Spring Rolls</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Crispy vegetable rolls (4 pcs)</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹180</span>
                                                        <span class="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">Veg</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="appetizers" data-item="caesar-salad" data-price="220">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-green-100 to-emerald-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🥗</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Caesar Salad</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Fresh romaine with dressing</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹220</span>
                                                        <span class="px-1.5 py-0.5 bg-green-100 text-green-700 text-xs font-medium rounded-full">Healthy</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="beverages" data-item="fresh-lime" data-price="80">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-cyan-100 to-blue-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍋</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Fresh Lime Water</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Sweet & sour refreshing drink</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹80</span>
                                                        <span class="px-1.5 py-0.5 bg-cyan-100 text-cyan-700 text-xs font-medium rounded-full">Fresh</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="beverages" data-item="masala-chai" data-price="60">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-amber-100 to-orange-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">☕</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Masala Chai</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Traditional spiced Indian tea</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹60</span>
                                                        <span class="px-1.5 py-0.5 bg-amber-100 text-amber-700 text-xs font-medium rounded-full">Hot</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="desserts" data-item="chocolate-cake" data-price="180">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-pink-100 to-rose-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍰</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Chocolate Cake</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Rich chocolate layer cake</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹180</span>
                                                        <span class="px-1.5 py-0.5 bg-pink-100 text-pink-700 text-xs font-medium rounded-full">Sweet</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="desserts" data-item="ice-cream" data-price="120">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍨</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Ice Cream</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Vanilla & chocolate scoops</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹120</span>
                                                        <span class="px-1.5 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-medium rounded-full">Cold</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Menu Items to Fill Space -->
                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="biryani" data-price="420">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-orange-100 to-red-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍛</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Chicken Biryani</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Aromatic basmati rice with chicken</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹420</span>
                                                        <span class="px-1.5 py-0.5 bg-red-100 text-red-700 text-xs font-medium rounded-full">Spicy</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="beverages" data-item="mango-lassi" data-price="120">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-100 to-orange-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🥭</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Mango Lassi</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Creamy yogurt mango drink</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹120</span>
                                                        <span class="px-1.5 py-0.5 bg-yellow-100 text-yellow-700 text-xs font-medium rounded-full">Creamy</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="appetizers" data-item="paneer-tikka" data-price="280">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-orange-100 to-amber-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🧀</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Paneer Tikka</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Grilled cottage cheese cubes</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹280</span>
                                                        <span class="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">Veg</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="fish-curry" data-price="480">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-100 to-cyan-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🐟</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Fish Curry</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Coastal style fish in coconut curry</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹480</span>
                                                        <span class="px-1.5 py-0.5 bg-cyan-100 text-cyan-700 text-xs font-medium rounded-full">Seafood</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="desserts" data-item="gulab-jamun" data-price="150">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-amber-100 to-yellow-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍯</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Gulab Jamun</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Sweet milk dumplings (3 pcs)</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹150</span>
                                                        <span class="px-1.5 py-0.5 bg-amber-100 text-amber-700 text-xs font-medium rounded-full">Traditional</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="beverages" data-item="cold-coffee" data-price="140">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-slate-100 to-amber-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🧊</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Cold Coffee</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Iced coffee with whipped cream</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹140</span>
                                                        <span class="px-1.5 py-0.5 bg-indigo-100 text-indigo-700 text-xs font-medium rounded-full">Cold</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="menu-item bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-indigo-300 hover:shadow-lg hover:from-indigo-50 hover:to-white transition-all duration-300 group" data-category="mains" data-item="dal-tadka" data-price="220">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-100 to-amber-200 rounded-lg flex items-center justify-center shadow-sm">
                                            <span class="text-xl">🍲</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-start justify-between">
                                                <div>
                                                    <h4 class="font-semibold text-slate-800 text-sm mb-0.5">Dal Tadka</h4>
                                                    <p class="text-xs text-slate-500 mb-1">Tempered yellow lentils</p>
                                                    <div class="flex items-center gap-2">
                                                        <span class="text-base font-bold text-indigo-600">₹220</span>
                                                        <span class="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">Veg</span>
                                                    </div>
                                                </div>
                                                <button class="add-item-btn w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg flex items-center justify-center hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 shadow-md shadow-indigo-500/25 group-hover:scale-105">
                                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Cart & Payment -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-slate-800">Order Cart</h3>
                                <div class="flex items-center gap-2">
                                    <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs font-medium rounded">Table 5</span>
                                    <button class="text-indigo-600 hover:text-indigo-800 text-sm">
                                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <div class="space-y-2 mb-4 max-h-48 overflow-y-auto" id="order-items">
                                <div class="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm">🍕</span>
                                        <div class="flex-1">
                                            <p class="font-medium text-slate-800 text-sm">Margherita Pizza</p>
                                            <p class="text-xs text-slate-500">₹450 each</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <button class="w-6 h-6 bg-slate-200 text-slate-600 rounded flex items-center justify-center hover:bg-slate-300 transition-colors">
                                            <i data-lucide="minus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="w-6 text-center font-medium text-sm">2</span>
                                        <button class="w-6 h-6 bg-indigo-600 text-white rounded flex items-center justify-center hover:bg-indigo-700 transition-colors">
                                            <i data-lucide="plus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="ml-2 font-bold text-slate-900 text-sm">₹900</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm">🍝</span>
                                        <div class="flex-1">
                                            <p class="font-medium text-slate-800 text-sm">Pasta Alfredo</p>
                                            <p class="text-xs text-slate-500">₹380 each</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <button class="w-6 h-6 bg-slate-200 text-slate-600 rounded flex items-center justify-center hover:bg-slate-300 transition-colors">
                                            <i data-lucide="minus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="w-6 text-center font-medium text-sm">1</span>
                                        <button class="w-6 h-6 bg-indigo-600 text-white rounded flex items-center justify-center hover:bg-indigo-700 transition-colors">
                                            <i data-lucide="plus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="ml-2 font-bold text-slate-900 text-sm">₹380</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm">🍋</span>
                                        <div class="flex-1">
                                            <p class="font-medium text-slate-800 text-sm">Fresh Lime</p>
                                            <p class="text-xs text-slate-500">₹80 each</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <button class="w-6 h-6 bg-slate-200 text-slate-600 rounded flex items-center justify-center hover:bg-slate-300 transition-colors">
                                            <i data-lucide="minus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="w-6 text-center font-medium text-sm">2</span>
                                        <button class="w-6 h-6 bg-indigo-600 text-white rounded flex items-center justify-center hover:bg-indigo-700 transition-colors">
                                            <i data-lucide="plus" class="w-3 h-3"></i>
                                        </button>
                                        <span class="ml-2 font-bold text-slate-900 text-sm">₹160</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Summary -->
                            <div class="border-t border-slate-200 pt-3 mb-4">
                                <div class="space-y-1">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600">Subtotal</span>
                                        <span class="font-medium">₹1,440</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-slate-600">Tax & Service</span>
                                        <span class="font-medium">₹216</span>
                                    </div>
                                    <div class="border-t border-slate-200 pt-2 mt-2">
                                        <div class="flex justify-between">
                                            <span class="font-semibold text-slate-800">Total</span>
                                            <span class="text-xl font-bold text-indigo-600">₹1,656</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Info -->
                            <div class="mb-4 p-3 bg-slate-50 rounded-lg border border-slate-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-slate-800 text-sm">Rahul Kumar</p>
                                        <p class="text-xs text-slate-500">+91 98765 43210 • 1,240 pts</p>
                                    </div>
                                    <i data-lucide="user" class="w-4 h-4 text-slate-400"></i>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="mb-4">
                                <h4 class="font-medium text-slate-800 mb-2 text-sm">Payment Method</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="payment-method active flex items-center justify-center gap-2 p-2.5 bg-indigo-100 border border-indigo-300 rounded-lg text-indigo-700 font-medium hover:bg-indigo-200 transition-colors text-sm">
                                        <i data-lucide="credit-card" class="w-4 h-4"></i>
                                        Card
                                    </button>
                                    <button class="payment-method flex items-center justify-center gap-2 p-2.5 bg-slate-100 border border-slate-300 rounded-lg text-slate-700 font-medium hover:bg-slate-200 transition-colors text-sm">
                                        <i data-lucide="banknote" class="w-4 h-4"></i>
                                        Cash
                                    </button>
                                    <button class="payment-method flex items-center justify-center gap-2 p-2.5 bg-slate-100 border border-slate-300 rounded-lg text-slate-700 font-medium hover:bg-slate-200 transition-colors text-sm">
                                        <i data-lucide="smartphone" class="w-4 h-4"></i>
                                        UPI
                                    </button>
                                    <button class="payment-method flex items-center justify-center gap-2 p-2.5 bg-slate-100 border border-slate-300 rounded-lg text-slate-700 font-medium hover:bg-slate-200 transition-colors text-sm">
                                        <i data-lucide="wallet" class="w-4 h-4"></i>
                                        Wallet
                                    </button>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-2">
                                <button class="w-full flex items-center justify-center gap-2 py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-200 font-medium shadow-sm">
                                    <i data-lucide="credit-card" class="w-4 h-4"></i>
                                    Process Payment
                                </button>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="flex items-center justify-center gap-1 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors font-medium text-sm">
                                        <i data-lucide="save" class="w-4 h-4"></i>
                                        Save
                                    </button>
                                    <button class="flex items-center justify-center gap-1 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-medium text-sm">
                                        <i data-lucide="x" class="w-4 h-4"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats & Recent Orders -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Today's Sales Stats -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="trending-up" class="w-5 h-5 text-indigo-600"></i>
                                Today's Sales
                            </h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <i data-lucide="dollar-sign" class="w-4 h-4 text-green-600"></i>
                                        <span class="text-sm font-medium text-green-700">Revenue</span>
                                    </div>
                                    <p class="text-2xl font-bold text-green-900">₹24,580</p>
                                    <p class="text-xs text-green-600">+12.5% vs yesterday</p>
                                </div>
                                <div class="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <i data-lucide="shopping-bag" class="w-4 h-4 text-blue-600"></i>
                                        <span class="text-sm font-medium text-blue-700">Orders</span>
                                    </div>
                                    <p class="text-2xl font-bold text-blue-900">156</p>
                                    <p class="text-xs text-blue-600">+8.3% vs yesterday</p>
                                </div>
                                <div class="p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <i data-lucide="users" class="w-4 h-4 text-purple-600"></i>
                                        <span class="text-sm font-medium text-purple-700">Customers</span>
                                    </div>
                                    <p class="text-2xl font-bold text-purple-900">89</p>
                                    <p class="text-xs text-purple-600">+15.2% vs yesterday</p>
                                </div>
                                <div class="p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-2 mb-2">
                                        <i data-lucide="clock" class="w-4 h-4 text-orange-600"></i>
                                        <span class="text-sm font-medium text-orange-700">Avg. Order</span>
                                    </div>
                                    <p class="text-2xl font-bold text-orange-900">₹485</p>
                                    <p class="text-xs text-orange-600">+3.7% vs yesterday</p>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Orders -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                    <i data-lucide="receipt" class="w-5 h-5 text-indigo-600"></i>
                                    Recent Orders
                                </h3>
                                <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-xs font-bold text-green-600">#155</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Table 3</p>
                                            <p class="text-xs text-slate-500">2 items • ₹680</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-xs font-bold text-blue-600">#154</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Table 7</p>
                                            <p class="text-xs text-slate-500">4 items • ₹1,240</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Preparing
                                    </span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <span class="text-xs font-bold text-orange-600">#153</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Table 2</p>
                                            <p class="text-xs text-slate-500">3 items • ₹890</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        Pending
                                    </span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <span class="text-xs font-bold text-purple-600">#152</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Table 1</p>
                                            <p class="text-xs text-slate-500">1 item • ₹450</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="customers-content" class="content-section">
                    <!-- Customer Management Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-blue-100 rounded-xl">
                                    <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Customer Management</h1>
                                    <p class="text-slate-600 text-sm">Comprehensive customer relationship management system</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Real-time sync
                                </span>
                                <span>1,247 total customers</span>
                                <span class="flex items-center gap-1">
                                    <i data-lucide="trending-up" class="w-4 h-4 text-green-500"></i>
                                    +12% this month
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg shadow-blue-500/25">
                                <i data-lucide="user-plus" class="w-4 h-4"></i>
                                Add Customer
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="mail" class="w-4 h-4"></i>
                                Send Campaign
                            </button>
                        </div>
                    </div>

                    <!-- Customer Analytics Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-blue-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12.3%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Customers</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">1,247</p>
                                <p class="text-xs text-slate-600">Active customers</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8.7%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Returning Customers</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">892</p>
                                <p class="text-xs text-slate-600">71.5% retention rate</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +0.3
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Average Rating</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">4.8</p>
                                <p class="text-xs text-slate-600">Customer satisfaction</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +15.2%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Avg. Order Value</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">₹485</p>
                                <p class="text-xs text-slate-600">Per customer</p>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Insights & Quick Actions -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Customer Segmentation -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="pie-chart" class="w-5 h-5 text-blue-600"></i>
                                Customer Segments
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="crown" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">VIP Customers</p>
                                            <p class="text-xs text-slate-500">₹10K+ lifetime value</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-green-600">127</p>
                                        <p class="text-xs text-green-500">10.2%</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="heart" class="w-5 h-5 text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Loyal Customers</p>
                                            <p class="text-xs text-slate-500">5+ visits per month</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-blue-600">456</p>
                                        <p class="text-xs text-blue-500">36.6%</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="user" class="w-5 h-5 text-yellow-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Regular Customers</p>
                                            <p class="text-xs text-slate-500">2-4 visits per month</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-yellow-600">664</p>
                                        <p class="text-xs text-yellow-500">53.2%</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Reviews -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="message-circle" class="w-5 h-5 text-purple-600"></i>
                                Recent Reviews
                            </h3>
                            <div class="space-y-4">
                                <div class="p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-start gap-3">
                                        <img class="w-8 h-8 rounded-full object-cover" src="https://placehold.co/32x32/E2E8F0/475569?text=SJ" alt="Customer">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <p class="text-sm font-medium text-slate-800">Sarah Johnson</p>
                                                <div class="flex items-center gap-1">
                                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                                    <span class="text-xs font-medium text-slate-600">5.0</span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-slate-600">"Amazing pasta! Will definitely come back."</p>
                                            <p class="text-xs text-slate-400 mt-1">2 hours ago</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-start gap-3">
                                        <img class="w-8 h-8 rounded-full object-cover" src="https://placehold.co/32x32/E2E8F0/475569?text=MD" alt="Customer">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <p class="text-sm font-medium text-slate-800">Mike Davis</p>
                                                <div class="flex items-center gap-1">
                                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                                    <span class="text-xs font-medium text-slate-600">4.8</span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-slate-600">"Great service and delicious food!"</p>
                                            <p class="text-xs text-slate-400 mt-1">5 hours ago</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-start gap-3">
                                        <img class="w-8 h-8 rounded-full object-cover" src="https://placehold.co/32x32/E2E8F0/475569?text=EM" alt="Customer">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <p class="text-sm font-medium text-slate-800">Emma Wilson</p>
                                                <div class="flex items-center gap-1">
                                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                                    <span class="text-xs font-medium text-slate-600">5.0</span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-slate-600">"Perfect ambiance for date night!"</p>
                                            <p class="text-xs text-slate-400 mt-1">1 day ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Loyalty Program -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="gift" class="w-5 h-5 text-orange-600"></i>
                                Loyalty Program
                            </h3>
                            <div class="space-y-4">
                                <div class="p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl border border-orange-200">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-orange-900">Active Members</h4>
                                        <span class="text-2xl font-bold text-orange-600">743</span>
                                    </div>
                                    <div class="w-full bg-orange-200 rounded-full h-2 mb-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 59.6%"></div>
                                    </div>
                                    <p class="text-xs text-orange-700">59.6% of total customers</p>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="star" class="w-3 h-3 text-yellow-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Points Redeemed</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">12,450</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                                <i data-lucide="gift" class="w-3 h-3 text-green-600"></i>
                                            </div>
                                            <span class="text-sm font-medium text-slate-700">Rewards Given</span>
                                        </div>
                                        <span class="text-sm font-bold text-slate-900">₹18,750</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-slate-200 mb-6">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                    <input type="text" id="customer-search" placeholder="Search customers by name, email, or phone..."
                                           class="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2">
                                <button class="customer-filter-btn active px-4 py-2.5 bg-blue-600 text-white rounded-lg text-sm font-medium" data-segment="all">
                                    All Customers
                                </button>
                                <button class="customer-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-segment="vip">
                                    VIP
                                </button>
                                <button class="customer-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-segment="loyal">
                                    Loyal
                                </button>
                                <button class="customer-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-segment="regular">
                                    Regular
                                </button>
                                <button class="customer-filter-btn px-4 py-2.5 bg-green-100 text-green-700 rounded-lg text-sm font-medium hover:bg-green-200" data-segment="active">
                                    Active Loyalty
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Table -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Customer Directory</h3>
                                    <p class="text-sm text-slate-500">1,247 total customers • 743 loyalty members</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="filter" class="w-4 h-4"></i>
                                        Filter
                                    </button>
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="sort-asc" class="w-4 h-4"></i>
                                        Sort
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-6 font-semibold text-slate-700 text-sm">Customer</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Contact</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Segment</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Orders</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Lifetime Value</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Last Visit</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Loyalty</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="customer-table-body" class="divide-y divide-slate-100">
                                    <!-- VIP Customer -->
                                    <tr class="hover:bg-blue-50 transition-colors" data-segment="vip">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <img class="w-10 h-10 rounded-full object-cover" src="https://placehold.co/40x40/E2E8F0/475569?text=RK" alt="Customer">
                                                <div>
                                                    <p class="font-semibold text-slate-900">Rahul Kumar</p>
                                                    <p class="text-xs text-slate-500">Since Jan 2022</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600"><EMAIL></p>
                                                <p class="text-xs text-slate-500">+91 98765 43210</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i data-lucide="crown" class="w-3 h-3 mr-1"></i>
                                                VIP
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-slate-900">48</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 90%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-green-600">₹12,480</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600">Yesterday</p>
                                                <p class="text-xs text-slate-500">7:30 PM</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                                <span class="font-medium text-slate-900">Gold • 1,240 pts</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="p-1.5 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors">
                                                    <i data-lucide="message-square" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors">
                                                    <i data-lucide="gift" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 transition-colors">
                                                    <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Loyal Customer -->
                                    <tr class="hover:bg-blue-50 transition-colors" data-segment="loyal">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <img class="w-10 h-10 rounded-full object-cover" src="https://placehold.co/40x40/E2E8F0/475569?text=PS" alt="Customer">
                                                <div>
                                                    <p class="font-semibold text-slate-900">Priya Sharma</p>
                                                    <p class="text-xs text-slate-500">Since Mar 2022</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600"><EMAIL></p>
                                                <p class="text-xs text-slate-500">+91 87654 32109</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i data-lucide="heart" class="w-3 h-3 mr-1"></i>
                                                Loyal
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-slate-900">32</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-green-600">₹8,640</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600">2 days ago</p>
                                                <p class="text-xs text-slate-500">1:15 PM</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <i data-lucide="star" class="w-4 h-4 text-blue-400 fill-current"></i>
                                                <span class="font-medium text-slate-900">Silver • 860 pts</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="p-1.5 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors">
                                                    <i data-lucide="message-square" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors">
                                                    <i data-lucide="gift" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 transition-colors">
                                                    <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Regular Customer -->
                                    <tr class="hover:bg-blue-50 transition-colors" data-segment="regular">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <img class="w-10 h-10 rounded-full object-cover" src="https://placehold.co/40x40/E2E8F0/475569?text=AP" alt="Customer">
                                                <div>
                                                    <p class="font-semibold text-slate-900">Arjun Patel</p>
                                                    <p class="text-xs text-slate-500">Since Jun 2022</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600"><EMAIL></p>
                                                <p class="text-xs text-slate-500">+91 76543 21098</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                                Regular
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-slate-900">18</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 40%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-green-600">₹4,860</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600">1 week ago</p>
                                                <p class="text-xs text-slate-500">6:45 PM</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <i data-lucide="star" class="w-4 h-4 text-slate-400 fill-current"></i>
                                                <span class="font-medium text-slate-900">Bronze • 420 pts</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="p-1.5 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors">
                                                    <i data-lucide="message-square" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors">
                                                    <i data-lucide="gift" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 transition-colors">
                                                    <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- New Customer -->
                                    <tr class="hover:bg-blue-50 transition-colors" data-segment="new">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <img class="w-10 h-10 rounded-full object-cover" src="https://placehold.co/40x40/E2E8F0/475569?text=SG" alt="Customer">
                                                <div>
                                                    <p class="font-semibold text-slate-900">Sneha Gupta</p>
                                                    <p class="text-xs text-slate-500">Since Aug 2023</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600"><EMAIL></p>
                                                <p class="text-xs text-slate-500">+91 65432 10987</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                <i data-lucide="user-plus" class="w-3 h-3 mr-1"></i>
                                                New
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-slate-900">3</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 10%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-green-600">₹810</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="text-sm text-slate-600">Today</p>
                                                <p class="text-xs text-slate-500">12:30 PM</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                    Not Enrolled
                                                </span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="p-1.5 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors">
                                                    <i data-lucide="message-square" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-orange-100 text-orange-600 rounded hover:bg-orange-200 transition-colors">
                                                    <i data-lucide="user-plus" class="w-4 h-4"></i>
                                                </button>
                                                <button class="p-1.5 bg-slate-100 text-slate-600 rounded hover:bg-slate-200 transition-colors">
                                                    <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Table Footer -->
                        <div class="px-6 py-4 border-t border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 4 of 1,247 customers
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Previous
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg">
                                        1
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        2
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        3
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="inventory-content" class="content-section">
                    <!-- Inventory Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-orange-100 rounded-xl">
                                    <i data-lucide="package" class="w-6 h-6 text-orange-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Inventory Management</h1>
                                    <p class="text-slate-600 text-sm">Real-time stock tracking and management system</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Live tracking
                                </span>
                                <span>Last updated: 5 minutes ago</span>
                                <span class="flex items-center gap-1">
                                    <i data-lucide="alert-triangle" class="w-4 h-4 text-orange-500"></i>
                                    3 items low stock
                                </span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-orange-600 to-orange-700 text-white rounded-xl hover:from-orange-700 hover:to-orange-800 transition-all duration-200 font-medium shadow-lg shadow-orange-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                Add Item
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="scan" class="w-4 h-4"></i>
                                Scan
                            </button>
                        </div>
                    </div>

                    <!-- Inventory Overview Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="package-2" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-blue-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +5.2%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Items</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">247</p>
                                <p class="text-xs text-slate-600">Active inventory</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                                    <i data-lucide="alert-triangle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-red-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                        Critical
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Low Stock Items</p>
                                <p class="text-3xl font-bold text-red-600 mb-1">3</p>
                                <p class="text-xs text-slate-600">Needs reorder</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12.8%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Inventory Value</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">₹1.2L</p>
                                <p class="text-xs text-slate-600">Total worth</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                        Active
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Pending Orders</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">8</p>
                                <p class="text-xs text-slate-600">Supplier orders</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions & Filters -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Quick Actions -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="zap" class="w-5 h-5 text-orange-600"></i>
                                Quick Actions
                            </h3>
                            <div class="space-y-3">
                                <button class="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 rounded-xl transition-all duration-200 text-left">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="shopping-cart" class="w-4 h-4 text-orange-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-slate-800">Reorder Low Stock</p>
                                        <p class="text-xs text-slate-500">3 items need reordering</p>
                                    </div>
                                </button>

                                <button class="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition-all duration-200 text-left">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="clipboard-check" class="w-4 h-4 text-blue-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-slate-800">Stock Audit</p>
                                        <p class="text-xs text-slate-500">Verify inventory counts</p>
                                    </div>
                                </button>

                                <button class="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 rounded-xl transition-all duration-200 text-left">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="file-text" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-slate-800">Generate Report</p>
                                        <p class="text-xs text-slate-500">Monthly inventory report</p>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Stock Alerts -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="bell" class="w-5 h-5 text-red-600"></i>
                                Stock Alerts
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-red-50 to-orange-50 rounded-xl">
                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="alert-circle" class="w-4 h-4 text-red-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-red-900">Tomatoes - Critical</p>
                                        <p class="text-xs text-red-700">Only 2kg left • Reorder now</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-yellow-900">Cheese - Low</p>
                                        <p class="text-xs text-yellow-700">5kg remaining • Order soon</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="clock" class="w-4 h-4 text-orange-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-orange-900">Milk - Expiring</p>
                                        <p class="text-xs text-orange-700">Expires in 2 days • Use first</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                                <i data-lucide="activity" class="w-5 h-5 text-green-600"></i>
                                Recent Activity
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="plus" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">Added 50kg Rice</p>
                                        <p class="text-xs text-slate-500">2 hours ago</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="minus" class="w-4 h-4 text-blue-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">Used 3kg Chicken</p>
                                        <p class="text-xs text-slate-500">4 hours ago</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="truck" class="w-4 h-4 text-purple-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">Order Delivered</p>
                                        <p class="text-xs text-slate-500">6 hours ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-slate-200 mb-6">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                    <input type="text" id="inventory-search" placeholder="Search inventory items..."
                                           class="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm">
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2">
                                <button class="inventory-filter-btn active px-4 py-2.5 bg-orange-600 text-white rounded-lg text-sm font-medium" data-category="all">
                                    All Items
                                </button>
                                <button class="inventory-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-category="vegetables">
                                    Vegetables
                                </button>
                                <button class="inventory-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-category="meat">
                                    Meat & Poultry
                                </button>
                                <button class="inventory-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-category="dairy">
                                    Dairy
                                </button>
                                <button class="inventory-filter-btn px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-200" data-category="grains">
                                    Grains
                                </button>
                                <button class="inventory-filter-btn px-4 py-2.5 bg-red-100 text-red-700 rounded-lg text-sm font-medium hover:bg-red-200" data-category="low-stock">
                                    Low Stock
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Inventory Items</h3>
                                    <p class="text-sm text-slate-500">247 total items • 3 low stock</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="filter" class="w-4 h-4"></i>
                                        Filter
                                    </button>
                                    <button class="flex items-center gap-2 px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        <i data-lucide="sort-asc" class="w-4 h-4"></i>
                                        Sort
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-6 font-semibold text-slate-700 text-sm">Item</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Category</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Current Stock</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Min. Level</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Unit Price</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Total Value</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-slate-700 text-sm">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="inventory-table-body" class="divide-y divide-slate-100">
                                    <!-- Critical Stock Items -->
                                    <tr class="hover:bg-red-50 transition-colors" data-category="vegetables" data-status="critical">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-lg">🍅</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Tomatoes</p>
                                                    <p class="text-xs text-slate-500">Fresh Roma Tomatoes</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Vegetables
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-red-600">2 kg</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-red-500 h-2 rounded-full" style="width: 10%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">20 kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-slate-900">₹80/kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-slate-900">₹160</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                                Critical
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-3 py-1.5 bg-orange-600 text-white text-xs rounded hover:bg-orange-700 transition-colors">
                                                    Reorder
                                                </button>
                                                <button class="px-3 py-1.5 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Edit
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Low Stock Items -->
                                    <tr class="hover:bg-yellow-50 transition-colors" data-category="dairy" data-status="low">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-lg">🧀</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Mozzarella Cheese</p>
                                                    <p class="text-xs text-slate-500">Premium Quality</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Dairy
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-yellow-600">5 kg</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 25%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">20 kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-slate-900">₹450/kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-slate-900">₹2,250</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>
                                                Low Stock
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-3 py-1.5 bg-orange-600 text-white text-xs rounded hover:bg-orange-700 transition-colors">
                                                    Reorder
                                                </button>
                                                <button class="px-3 py-1.5 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Edit
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Normal Stock Items -->
                                    <tr class="hover:bg-slate-50 transition-colors" data-category="meat" data-status="normal">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-lg">🍗</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Chicken Breast</p>
                                                    <p class="text-xs text-slate-500">Fresh Boneless</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Meat & Poultry
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-green-600">25 kg</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-green-500 h-2 rounded-full" style="width: 83%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">10 kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-slate-900">₹280/kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-slate-900">₹7,000</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                                In Stock
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-3 py-1.5 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors">
                                                    Update
                                                </button>
                                                <button class="px-3 py-1.5 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Edit
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-category="grains" data-status="normal">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-lg">🌾</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Basmati Rice</p>
                                                    <p class="text-xs text-slate-500">Premium Long Grain</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                                Grains
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-green-600">75 kg</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-green-500 h-2 rounded-full" style="width: 75%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">25 kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-slate-900">₹120/kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-slate-900">₹9,000</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                                In Stock
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-3 py-1.5 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors">
                                                    Update
                                                </button>
                                                <button class="px-3 py-1.5 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Edit
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-category="vegetables" data-status="normal">
                                        <td class="py-4 px-6">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-lg">🥬</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-slate-900">Fresh Lettuce</p>
                                                    <p class="text-xs text-slate-500">Iceberg Lettuce</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Vegetables
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex items-center gap-2">
                                                <span class="font-bold text-green-600">15 kg</span>
                                                <div class="w-16 bg-slate-200 rounded-full h-2">
                                                    <div class="bg-green-500 h-2 rounded-full" style="width: 60%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-slate-600">8 kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-medium text-slate-900">₹60/kg</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="font-bold text-slate-900">₹900</span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                                In Stock
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-3 py-1.5 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors">
                                                    Update
                                                </button>
                                                <button class="px-3 py-1.5 bg-slate-100 text-slate-600 text-xs rounded hover:bg-slate-200 transition-colors">
                                                    Edit
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Table Footer -->
                        <div class="px-6 py-4 border-t border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 5 of 247 items
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Previous
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium bg-orange-600 text-white rounded-lg">
                                        1
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        2
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        3
                                    </button>
                                    <button class="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="analytics-content" class="content-section">
                    <!-- Analytics Header -->
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-purple-100 rounded-xl">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Analytics Dashboard</h1>
                                    <p class="text-slate-600 text-sm">Comprehensive business insights and performance metrics</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Real-time data
                                </span>
                                <span>Last updated: 2 minutes ago</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 font-medium shadow-lg shadow-purple-500/25">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export Report
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="calendar" class="w-4 h-4"></i>
                                Date Range
                            </button>
                        </div>
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +15.3%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Revenue</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">₹2,45,890</p>
                                <p class="text-xs text-slate-600">This month</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-blue-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8.7%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Customers</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">1,247</p>
                                <p class="text-xs text-slate-600">Active customers</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="shopping-bag" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12.1%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Average Order</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">₹485</p>
                                <p class="text-xs text-slate-600">Per order value</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +0.2
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Customer Rating</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">4.8</p>
                                <p class="text-xs text-slate-600">Out of 5.0</p>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Professional Revenue Bar Chart -->
                        <div class="chart-container bg-white rounded-2xl p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-all duration-300">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <div class="w-2 h-6 bg-gradient-to-b from-purple-500 to-blue-500 rounded-full"></div>
                                        Monthly Revenue
                                    </h3>
                                    <p class="text-sm text-slate-500">Last 6 months performance • ₹15.5L total</p>
                                </div>
                                <div class="flex gap-2">
                                    <button class="px-4 py-2 text-xs font-semibold bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">6M</button>
                                    <button class="px-4 py-2 text-xs font-medium text-slate-600 bg-slate-100 hover:bg-slate-200 rounded-lg transition-all duration-200">1Y</button>
                                </div>
                            </div>

                            <div class="relative h-64">
                                <svg width="100%" height="100%" viewBox="0 0 500 240" class="absolute inset-0">
                                    <!-- Grid Lines -->
                                    <defs>
                                        <linearGradient id="gridGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:0.3" />
                                        </linearGradient>
                                        <linearGradient id="barGradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="barGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="barGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="barGradient4" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="barGradient5" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="barGradient6" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Horizontal Grid Lines -->
                                    <line x1="60" y1="40" x2="460" y2="40" stroke="url(#gridGradient)" stroke-width="1" class="chart-grid"/>
                                    <line x1="60" y1="80" x2="460" y2="80" stroke="url(#gridGradient)" stroke-width="1" class="chart-grid"/>
                                    <line x1="60" y1="120" x2="460" y2="120" stroke="url(#gridGradient)" stroke-width="1" class="chart-grid"/>
                                    <line x1="60" y1="160" x2="460" y2="160" stroke="url(#gridGradient)" stroke-width="1" class="chart-grid"/>
                                    <line x1="60" y1="200" x2="460" y2="200" stroke="url(#gridGradient)" stroke-width="1" class="chart-grid"/>

                                    <!-- Y-axis labels -->
                                    <text x="50" y="45" class="chart-label" text-anchor="end">₹3L</text>
                                    <text x="50" y="85" class="chart-label" text-anchor="end">₹2.5L</text>
                                    <text x="50" y="125" class="chart-label" text-anchor="end">₹2L</text>
                                    <text x="50" y="165" class="chart-label" text-anchor="end">₹1.5L</text>
                                    <text x="50" y="205" class="chart-label" text-anchor="end">₹1L</text>

                                    <!-- Bars with animations -->
                                    <rect x="80" y="104" width="32" height="96" rx="4" fill="url(#barGradient1)" class="chart-bar" style="animation-delay: 0.1s"/>
                                    <rect x="140" y="88" width="32" height="112" rx="4" fill="url(#barGradient2)" class="chart-bar" style="animation-delay: 0.2s"/>
                                    <rect x="200" y="140" width="32" height="60" rx="4" fill="url(#barGradient3)" class="chart-bar" style="animation-delay: 0.3s"/>
                                    <rect x="260" y="56" width="32" height="144" rx="4" fill="url(#barGradient4)" class="chart-bar" style="animation-delay: 0.4s"/>
                                    <rect x="320" y="72" width="32" height="128" rx="4" fill="url(#barGradient5)" class="chart-bar" style="animation-delay: 0.5s"/>
                                    <rect x="380" y="40" width="32" height="160" rx="4" fill="url(#barGradient6)" class="chart-bar" style="animation-delay: 0.6s"/>

                                    <!-- X-axis labels -->
                                    <text x="96" y="225" class="chart-label font-medium" text-anchor="middle">Jan</text>
                                    <text x="156" y="225" class="chart-label font-medium" text-anchor="middle">Feb</text>
                                    <text x="216" y="225" class="chart-label font-medium" text-anchor="middle">Mar</text>
                                    <text x="276" y="225" class="chart-label font-medium" text-anchor="middle">Apr</text>
                                    <text x="336" y="225" class="chart-label font-medium" text-anchor="middle">May</text>
                                    <text x="396" y="225" class="chart-label font-medium" text-anchor="middle">Jun</text>
                                </svg>

                                <!-- Value labels on bars -->
                                <div class="absolute inset-0 pointer-events-none">
                                    <div class="absolute" style="left: 96px; top: 90px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹1.8L</span>
                                    </div>
                                    <div class="absolute" style="left: 156px; top: 74px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹2.1L</span>
                                    </div>
                                    <div class="absolute" style="left: 216px; top: 126px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹1.5L</span>
                                    </div>
                                    <div class="absolute" style="left: 276px; top: 42px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹2.7L</span>
                                    </div>
                                    <div class="absolute" style="left: 336px; top: 58px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹2.4L</span>
                                    </div>
                                    <div class="absolute" style="left: 396px; top: 26px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-black bg-opacity-70 px-2 py-1 rounded">₹3.0L</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Chart Footer -->
                            <div class="flex items-center justify-between mt-4 pt-4 border-t border-slate-100">
                                <div class="flex items-center gap-4 text-sm">
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
                                        <span class="text-slate-600">Revenue Growth</span>
                                    </div>
                                    <div class="flex items-center gap-1 text-green-600 font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +23.5% vs last period
                                    </div>
                                </div>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center gap-1">
                                    View Details
                                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Professional Pie Chart - Order Distribution -->
                        <div class="chart-container bg-white rounded-2xl p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-all duration-300">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <div class="w-2 h-6 bg-gradient-to-b from-green-500 to-blue-500 rounded-full"></div>
                                        Order Distribution
                                    </h3>
                                    <p class="text-sm text-slate-500">By meal type • 1,247 total orders</p>
                                </div>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center gap-1">
                                    View Details
                                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                                </button>
                            </div>

                            <div class="flex items-center justify-center h-64 relative">
                                <!-- Professional Pie Chart -->
                                <div class="relative">
                                    <svg width="220" height="220" viewBox="0 0 220 220" class="transform -rotate-90 drop-shadow-lg">
                                        <defs>
                                            <linearGradient id="breakfastGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                                            </linearGradient>
                                            <linearGradient id="lunchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
                                            </linearGradient>
                                            <linearGradient id="dinnerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                                            </linearGradient>
                                            <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                                                <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.1)"/>
                                            </filter>
                                        </defs>

                                        <!-- Breakfast - 25% -->
                                        <circle cx="110" cy="110" r="85" fill="none" stroke="url(#breakfastGradient)" stroke-width="24"
                                                stroke-dasharray="133.5 502" stroke-dashoffset="0" class="pie-segment" filter="url(#shadow)"
                                                style="animation: drawPie 1.5s ease-out 0.2s both;"></circle>

                                        <!-- Lunch - 45% -->
                                        <circle cx="110" cy="110" r="85" fill="none" stroke="url(#lunchGradient)" stroke-width="24"
                                                stroke-dasharray="240.3 502" stroke-dashoffset="-133.5" class="pie-segment" filter="url(#shadow)"
                                                style="animation: drawPie 1.5s ease-out 0.4s both;"></circle>

                                        <!-- Dinner - 30% -->
                                        <circle cx="110" cy="110" r="85" fill="none" stroke="url(#dinnerGradient)" stroke-width="24"
                                                stroke-dasharray="160.2 502" stroke-dashoffset="-373.8" class="pie-segment" filter="url(#shadow)"
                                                style="animation: drawPie 1.5s ease-out 0.6s both;"></circle>
                                    </svg>

                                    <!-- Center Content -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center bg-white rounded-full w-24 h-24 flex items-center justify-center shadow-lg">
                                            <div>
                                                <p class="text-2xl font-bold text-slate-900">1,247</p>
                                                <p class="text-xs text-slate-500 font-medium">Orders</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Legend -->
                            <div class="space-y-3 mt-6">
                                <div class="chart-legend-item flex items-center justify-between p-3 rounded-xl hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 transition-all duration-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full shadow-sm"></div>
                                        <div>
                                            <span class="text-sm font-medium text-slate-700">Breakfast</span>
                                            <p class="text-xs text-slate-500">6:00 AM - 11:00 AM</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-sm font-bold text-slate-900">312</span>
                                        <p class="text-xs text-yellow-600 font-medium">25%</p>
                                    </div>
                                </div>

                                <div class="chart-legend-item flex items-center justify-between p-3 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 bg-gradient-to-r from-green-400 to-green-500 rounded-full shadow-sm"></div>
                                        <div>
                                            <span class="text-sm font-medium text-slate-700">Lunch</span>
                                            <p class="text-xs text-slate-500">11:00 AM - 4:00 PM</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-sm font-bold text-slate-900">561</span>
                                        <p class="text-xs text-green-600 font-medium">45%</p>
                                    </div>
                                </div>

                                <div class="chart-legend-item flex items-center justify-between p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 transition-all duration-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-sm"></div>
                                        <div>
                                            <span class="text-sm font-medium text-slate-700">Dinner</span>
                                            <p class="text-xs text-slate-500">4:00 PM - 11:00 PM</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-sm font-bold text-slate-900">374</span>
                                        <p class="text-xs text-blue-600 font-medium">30%</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Professional Line Chart - Customer Growth -->
                        <div class="chart-container bg-white rounded-2xl p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-all duration-300">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <div class="w-2 h-6 bg-gradient-to-b from-emerald-500 to-teal-500 rounded-full"></div>
                                        Customer Growth
                                    </h3>
                                    <p class="text-sm text-slate-500">Weekly new customers • 847 total this period</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="flex items-center gap-2 text-emerald-600 text-sm font-medium bg-emerald-50 px-3 py-1 rounded-lg">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12.5%
                                    </div>
                                    <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">Export</button>
                                </div>
                            </div>

                            <div class="h-64 relative">
                                <svg width="100%" height="100%" viewBox="0 0 500 240" class="absolute inset-0">
                                    <defs>
                                        <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                            <stop offset="50%" style="stop-color:#06b6d4;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.3" />
                                            <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.05" />
                                        </linearGradient>
                                        <filter id="glow">
                                            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                            <feMerge>
                                                <feMergeNode in="coloredBlur"/>
                                                <feMergeNode in="SourceGraphic"/>
                                            </feMerge>
                                        </filter>
                                    </defs>

                                    <!-- Grid -->
                                    <g class="chart-grid">
                                        <line x1="60" y1="40" x2="460" y2="40" stroke="#e2e8f0" stroke-width="1"/>
                                        <line x1="60" y1="80" x2="460" y2="80" stroke="#e2e8f0" stroke-width="1"/>
                                        <line x1="60" y1="120" x2="460" y2="120" stroke="#e2e8f0" stroke-width="1"/>
                                        <line x1="60" y1="160" x2="460" y2="160" stroke="#e2e8f0" stroke-width="1"/>
                                        <line x1="60" y1="200" x2="460" y2="200" stroke="#e2e8f0" stroke-width="1"/>
                                    </g>

                                    <!-- Y-axis labels -->
                                    <text x="50" y="45" class="chart-label" text-anchor="end">120</text>
                                    <text x="50" y="85" class="chart-label" text-anchor="end">100</text>
                                    <text x="50" y="125" class="chart-label" text-anchor="end">80</text>
                                    <text x="50" y="165" class="chart-label" text-anchor="end">60</text>
                                    <text x="50" y="205" class="chart-label" text-anchor="end">40</text>

                                    <!-- Area under curve -->
                                    <path d="M 80,180 L 120,160 L 160,140 L 200,120 L 240,100 L 280,85 L 320,70 L 360,55 L 400,45 L 440,35 L 440,200 L 80,200 Z"
                                          fill="url(#areaGradient)" opacity="0.6"/>

                                    <!-- Main line -->
                                    <path d="M 80,180 L 120,160 L 160,140 L 200,120 L 240,100 L 280,85 L 320,70 L 360,55 L 400,45 L 440,35"
                                          fill="none" stroke="url(#lineGradient)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"
                                          class="chart-line" filter="url(#glow)"/>

                                    <!-- Data points with hover effects -->
                                    <circle cx="80" cy="180" r="6" fill="#10b981" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="120" cy="160" r="6" fill="#10b981" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="160" cy="140" r="6" fill="#059669" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="200" cy="120" r="6" fill="#06b6d4" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="240" cy="100" r="6" fill="#0891b2" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="280" cy="85" r="6" fill="#0284c7" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="320" cy="70" r="6" fill="#0369a1" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="360" cy="55" r="6" fill="#7c3aed" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="400" cy="45" r="6" fill="#8b5cf6" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>
                                    <circle cx="440" cy="35" r="6" fill="#a855f7" stroke="white" stroke-width="3" class="chart-point" filter="url(#glow)"/>

                                    <!-- X-axis labels -->
                                    <text x="80" y="225" class="chart-label font-medium" text-anchor="middle">W1</text>
                                    <text x="120" y="225" class="chart-label font-medium" text-anchor="middle">W2</text>
                                    <text x="160" y="225" class="chart-label font-medium" text-anchor="middle">W3</text>
                                    <text x="200" y="225" class="chart-label font-medium" text-anchor="middle">W4</text>
                                    <text x="240" y="225" class="chart-label font-medium" text-anchor="middle">W5</text>
                                    <text x="280" y="225" class="chart-label font-medium" text-anchor="middle">W6</text>
                                    <text x="320" y="225" class="chart-label font-medium" text-anchor="middle">W7</text>
                                    <text x="360" y="225" class="chart-label font-medium" text-anchor="middle">W8</text>
                                    <text x="400" y="225" class="chart-label font-medium" text-anchor="middle">W9</text>
                                    <text x="440" y="225" class="chart-label font-medium" text-anchor="middle">W10</text>
                                </svg>

                                <!-- Value tooltips -->
                                <div class="absolute inset-0 pointer-events-none">
                                    <div class="absolute" style="left: 80px; top: 165px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-emerald-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">45</span>
                                    </div>
                                    <div class="absolute" style="left: 120px; top: 145px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-emerald-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">52</span>
                                    </div>
                                    <div class="absolute" style="left: 160px; top: 125px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-emerald-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">61</span>
                                    </div>
                                    <div class="absolute" style="left: 200px; top: 105px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-cyan-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">68</span>
                                    </div>
                                    <div class="absolute" style="left: 240px; top: 85px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-cyan-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">75</span>
                                    </div>
                                    <div class="absolute" style="left: 280px; top: 70px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-cyan-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">82</span>
                                    </div>
                                    <div class="absolute" style="left: 320px; top: 55px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-blue-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">89</span>
                                    </div>
                                    <div class="absolute" style="left: 360px; top: 40px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-purple-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">96</span>
                                    </div>
                                    <div class="absolute" style="left: 400px; top: 30px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-purple-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">103</span>
                                    </div>
                                    <div class="absolute" style="left: 440px; top: 20px; transform: translateX(-50%);">
                                        <span class="text-xs font-bold text-white bg-purple-600 px-2 py-1 rounded shadow-lg opacity-0 hover:opacity-100 transition-opacity">110</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Chart Footer -->
                            <div class="flex items-center justify-between mt-4 pt-4 border-t border-slate-100">
                                <div class="flex items-center gap-4 text-sm">
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-gradient-to-r from-emerald-500 to-purple-500 rounded-full"></div>
                                        <span class="text-slate-600">Weekly Growth</span>
                                    </div>
                                    <div class="text-slate-500">
                                        Average: <span class="font-semibold text-slate-700">74 customers/week</span>
                                    </div>
                                </div>
                                <div class="text-sm text-slate-500">
                                    Peak: Week 10 (110 customers)
                                </div>
                            </div>
                        </div>

                        <!-- Professional Donut Chart - Payment Methods -->
                        <div class="chart-container bg-white rounded-2xl p-6 shadow-lg border border-slate-200 hover:shadow-xl transition-all duration-300">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
                                        <div class="w-2 h-6 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
                                        Payment Methods
                                    </h3>
                                    <p class="text-sm text-slate-500">Transaction breakdown • ₹2.4L total</p>
                                </div>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center gap-1">
                                    Details
                                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                                </button>
                            </div>

                            <div class="flex items-center justify-center h-64 relative">
                                <!-- Professional Donut Chart -->
                                <div class="relative">
                                    <svg width="200" height="200" viewBox="0 0 200 200" class="transform -rotate-90 drop-shadow-lg">
                                        <defs>
                                            <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
                                            </linearGradient>
                                            <linearGradient id="cashGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#15803d;stop-opacity:1" />
                                            </linearGradient>
                                            <linearGradient id="upiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
                                            </linearGradient>
                                            <filter id="donutShadow" x="-50%" y="-50%" width="200%" height="200%">
                                                <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0,0,0,0.1)"/>
                                            </filter>
                                        </defs>

                                        <!-- Card Payment - 45% -->
                                        <circle cx="100" cy="100" r="70" fill="none" stroke="url(#cardGradient)" stroke-width="28"
                                                stroke-dasharray="197.9 439.8" stroke-dashoffset="0" class="donut-segment" filter="url(#donutShadow)"
                                                style="animation: drawPie 1.8s ease-out 0.3s both;"></circle>

                                        <!-- Cash - 30% -->
                                        <circle cx="100" cy="100" r="70" fill="none" stroke="url(#cashGradient)" stroke-width="28"
                                                stroke-dasharray="131.9 439.8" stroke-dashoffset="-197.9" class="donut-segment" filter="url(#donutShadow)"
                                                style="animation: drawPie 1.8s ease-out 0.5s both;"></circle>

                                        <!-- UPI/Digital - 25% -->
                                        <circle cx="100" cy="100" r="70" fill="none" stroke="url(#upiGradient)" stroke-width="28"
                                                stroke-dasharray="110 439.8" stroke-dashoffset="-329.8" class="donut-segment" filter="url(#donutShadow)"
                                                style="animation: drawPie 1.8s ease-out 0.7s both;"></circle>
                                    </svg>

                                    <!-- Center Content with enhanced styling -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center bg-white rounded-full w-28 h-28 flex items-center justify-center shadow-xl border-4 border-slate-50">
                                            <div>
                                                <p class="text-2xl font-bold text-slate-900">₹2.4L</p>
                                                <p class="text-xs text-slate-500 font-medium">Total</p>
                                                <div class="flex items-center justify-center gap-1 mt-1">
                                                    <div class="w-1 h-1 bg-green-500 rounded-full"></div>
                                                    <span class="text-xs text-green-600 font-medium">+8.2%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Legend with detailed breakdown -->
                            <div class="space-y-4 mt-6">
                                <div class="chart-legend-item flex items-center justify-between p-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 border border-transparent hover:border-blue-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-5 h-5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-sm flex items-center justify-center">
                                            <i data-lucide="credit-card" class="w-3 h-3 text-white"></i>
                                        </div>
                                        <div>
                                            <span class="text-sm font-semibold text-slate-700">Card Payment</span>
                                            <p class="text-xs text-slate-500">Debit & Credit Cards</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-lg font-bold text-slate-900">₹1.08L</span>
                                        <p class="text-xs text-blue-600 font-semibold">45%</p>
                                    </div>
                                </div>

                                <div class="chart-legend-item flex items-center justify-between p-4 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-200 border border-transparent hover:border-green-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-5 h-5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-sm flex items-center justify-center">
                                            <i data-lucide="banknote" class="w-3 h-3 text-white"></i>
                                        </div>
                                        <div>
                                            <span class="text-sm font-semibold text-slate-700">Cash</span>
                                            <p class="text-xs text-slate-500">Physical Currency</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-lg font-bold text-slate-900">₹72K</span>
                                        <p class="text-xs text-green-600 font-semibold">30%</p>
                                    </div>
                                </div>

                                <div class="chart-legend-item flex items-center justify-between p-4 rounded-xl hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 transition-all duration-200 border border-transparent hover:border-yellow-200">
                                    <div class="flex items-center gap-3">
                                        <div class="w-5 h-5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg shadow-sm flex items-center justify-center">
                                            <i data-lucide="smartphone" class="w-3 h-3 text-white"></i>
                                        </div>
                                        <div>
                                            <span class="text-sm font-semibold text-slate-700">UPI/Digital</span>
                                            <p class="text-xs text-slate-500">UPI, Wallets & Online</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-lg font-bold text-slate-900">₹60K</span>
                                        <p class="text-xs text-yellow-600 font-semibold">25%</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional insights -->
                            <div class="mt-6 pt-4 border-t border-slate-100">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-slate-600">Most Popular:</span>
                                    <span class="font-semibold text-blue-600">Card Payments</span>
                                </div>
                                <div class="flex items-center justify-between text-sm mt-1">
                                    <span class="text-slate-600">Growth vs last month:</span>
                                    <span class="font-semibold text-green-600">+8.2%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Top Performing Items -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Top Performers</h3>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍕</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Margherita Pizza</p>
                                            <p class="text-xs text-slate-500">156 orders this month</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-green-600">₹6,240</p>
                                        <p class="text-xs text-green-500">+18%</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍝</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Pasta Alfredo</p>
                                            <p class="text-xs text-slate-500">142 orders this month</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-blue-600">₹5,680</p>
                                        <p class="text-xs text-blue-500">+12%</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍔</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Classic Burger</p>
                                            <p class="text-xs text-slate-500">128 orders this month</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-orange-600">₹5,120</p>
                                        <p class="text-xs text-orange-500">+8%</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Insights -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Customer Insights</h3>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">Details</button>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-slate-700">New Customers</span>
                                        <span class="text-sm font-semibold text-slate-900">23%</span>
                                    </div>
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style="width: 23%"></div>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-slate-700">Returning Customers</span>
                                        <span class="text-sm font-semibold text-slate-900">77%</span>
                                    </div>
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full" style="width: 77%"></div>
                                    </div>
                                </div>

                                <div class="pt-4 border-t border-slate-100">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-sm font-medium text-slate-700">Customer Satisfaction</span>
                                        <div class="flex items-center gap-1">
                                            <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                            <span class="text-sm font-semibold text-slate-900">4.8/5</span>
                                        </div>
                                    </div>
                                    <p class="text-xs text-slate-500">Based on 342 reviews this month</p>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Summary -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Financial Summary</h3>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">Export</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div>
                                        <p class="text-sm font-medium text-slate-700">Gross Revenue</p>
                                        <p class="text-lg font-bold text-slate-900">₹2,45,890</p>
                                    </div>
                                    <div class="text-green-600">
                                        <i data-lucide="trending-up" class="w-5 h-5"></i>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div>
                                        <p class="text-sm font-medium text-slate-700">Operating Costs</p>
                                        <p class="text-lg font-bold text-slate-900">₹1,12,340</p>
                                    </div>
                                    <div class="text-orange-600">
                                        <i data-lucide="trending-down" class="w-5 h-5"></i>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div>
                                        <p class="text-sm font-medium text-slate-700">Net Profit</p>
                                        <p class="text-lg font-bold text-slate-900">₹1,33,550</p>
                                    </div>
                                    <div class="text-blue-600">
                                        <i data-lucide="trending-up" class="w-5 h-5"></i>
                                    </div>
                                </div>

                                <div class="pt-3 border-t border-slate-100">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-slate-700">Profit Margin</span>
                                        <span class="text-sm font-bold text-green-600">54.3%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity & Alerts -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Recent Activity -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Recent Activity</h3>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="trending-up" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">Revenue milestone reached</p>
                                        <p class="text-xs text-slate-500">Monthly target of ₹2.5L achieved</p>
                                        <p class="text-xs text-slate-400">2 hours ago</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="star" class="w-4 h-4 text-blue-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">New 5-star review received</p>
                                        <p class="text-xs text-slate-500">Customer praised the pasta quality</p>
                                        <p class="text-xs text-slate-400">4 hours ago</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 hover:bg-slate-50 rounded-xl transition-colors">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="users" class="w-4 h-4 text-purple-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-slate-900">Peak hour traffic detected</p>
                                        <p class="text-xs text-slate-500">Lunch rush: 45 orders in 1 hour</p>
                                        <p class="text-xs text-slate-400">6 hours ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Alerts -->
                        <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Performance Alerts</h3>
                                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">Settings</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="check-circle" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-green-900">Sales Target Achieved</p>
                                        <p class="text-xs text-green-700">Monthly goal reached 3 days early</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-yellow-900">Inventory Alert</p>
                                        <p class="text-xs text-yellow-700">Tomatoes running low (2 days left)</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <i data-lucide="info" class="w-4 h-4 text-blue-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-blue-900">Peak Hours Identified</p>
                                        <p class="text-xs text-blue-700">Consider staff scheduling for 12-2 PM</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="settings-content" class="content-section">
                     <p class="p-6">Settings page will be here.</p>
                </div>
            </main>
        </div>
    </div>

    <div id="add-task-modal" class="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md m-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-slate-800">Add a New Task</h3>
                <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <form id="add-task-form">
                <div class="space-y-4">
                    <div>
                        <label for="task-title" class="block text-sm font-medium text-slate-700">Title</label>
                        <input type="text" id="task-title" name="title" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="task-description" class="block text-sm font-medium text-slate-700">Description</label>
                        <textarea id="task-description" name="description" rows="3" class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    <div>
                        <label for="task-category" class="block text-sm font-medium text-slate-700">Category</label>
                        <input type="text" id="task-category" name="category" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Cleaning, Inventory">
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" id="cancel-modal-btn" class="px-4 py-2 text-sm font-medium text-slate-700 bg-slate-100 rounded-md hover:bg-slate-200">Cancel</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Save Task</button>
                </div>
            </form>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Lucide Icons
            lucide.createIcons();
            
            // Check authentication and load user data
            const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');

            // Redirect to login if not authenticated
            if (!userData.email || !userData.isLoggedIn) {
                window.location.href = './login.html';
                return;
            }

            // User interface setup function
            function setupUserInterface(userData) {
                const roleIcons = {
                    admin: '👨‍💼',
                    manager: '👩‍💼',
                    staff: '👨‍🍳',
                    waiter: '🧑‍🍳'
                };

                const roleNames = {
                    admin: 'Admin',
                    manager: 'Manager',
                    staff: 'Staff',
                    waiter: 'Waiter'
                };

                // Update role badge
                const roleIcon = document.getElementById('role-icon');
                const roleText = document.getElementById('role-text');
                const roleBadge = document.getElementById('user-role-badge');

                if (roleIcon && roleText && userData.role) {
                    roleIcon.textContent = roleIcons[userData.role] || '👤';
                    roleText.textContent = roleNames[userData.role] || userData.role;
                    roleBadge.classList.remove('hidden');
                }

                // Update user menu
                const userNameEl = document.querySelector('#user-menu #user-name');
                const userEmailEl = document.querySelector('#user-menu #user-email');
                const userAvatar = document.getElementById('user-avatar');

                if (userNameEl) userNameEl.textContent = userData.name || 'User';
                if (userEmailEl) userEmailEl.textContent = userData.email || '';

                // Update avatar with role-based initials
                if (userAvatar) {
                    const initials = (userData.name || userData.role || 'U').substring(0, 2).toUpperCase();
                    userAvatar.src = `https://placehold.co/100x100/2563eb/ffffff?text=${initials}`;
                }

                // Setup user menu toggle
                const userMenuBtn = document.getElementById('user-menu-btn');
                const userMenu = document.getElementById('user-menu');

                if (userMenuBtn && userMenu) {
                    userMenuBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        userMenu.classList.toggle('hidden');
                    });

                    // Close menu when clicking outside
                    document.addEventListener('click', () => {
                        userMenu.classList.add('hidden');
                    });

                    userMenu.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });
                }
                
                // Setup logout functionality
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', () => {
                        const confirmed = confirm('Are you sure you want to logout?');
                        if (confirmed) {
                            const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');
                            userData.isLoggedIn = false;
                            localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                            window.location.href = './login.html';
                        }
                    });
                }
            }
            
            setupUserInterface(userData);
            
            // Check for new user and show welcome banner
            const welcomeBanner = document.getElementById('welcome-banner');
            const userNameSpan = document.getElementById('user-name-banner');
            const dismissWelcome = document.getElementById('dismiss-welcome');

            if (userData.isNewUser && userData.firstName) {
                welcomeBanner.classList.remove('hidden');
                userNameSpan.textContent = userData.firstName;

                dismissWelcome.addEventListener('click', () => {
                    welcomeBanner.classList.add('hidden');
                    userData.isNewUser = false;
                    localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                });
            }

            // --- DOM Elements ---
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebarBackdrop = document.getElementById('sidebar-backdrop');
            
            const sidebarLinks = document.querySelectorAll('.sidebar-item');
            const contentSections = document.querySelectorAll('.content-section');
            const headerTitle = document.getElementById('header-title');
            
            const SIDEBAR_STATE_KEY = 'sidebarCollapsed';

            // --- Sidebar Logic ---
            const applySidebarState = (isCollapsed) => {
                sidebar.classList.toggle('collapsed', isCollapsed);
            };

            const toggleDesktopSidebar = () => {
                const isCollapsed = !sidebar.classList.contains('collapsed');
                applySidebarState(isCollapsed);
                localStorage.setItem(SIDEBAR_STATE_KEY, isCollapsed.toString());
            };

            if(sidebarToggle) sidebarToggle.addEventListener('click', toggleDesktopSidebar);

            const toggleMobileSidebar = () => {
                sidebar.classList.toggle('open');
                sidebarBackdrop.classList.toggle('hidden');
            };

            if(mobileMenuButton) mobileMenuButton.addEventListener('click', toggleMobileSidebar);
            if(sidebarBackdrop) sidebarBackdrop.addEventListener('click', toggleMobileSidebar);

            const initSidebar = () => {
                const isDesktop = window.innerWidth >= 1024;
                if (isDesktop) {
                    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY) === 'true';
                    applySidebarState(savedState);
                    sidebar.classList.remove('open');
                    sidebarBackdrop.classList.add('hidden');
                } else {
                    sidebar.classList.add('open');
                    sidebar.classList.remove('collapsed');
                }
            };
            
            // --- Navigation Logic ---
            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const targetId = e.currentTarget.dataset.target;
                    const targetContent = document.getElementById(targetId + '-content');
                    const linkTextSpan = e.currentTarget.querySelector('.sidebar-link-text');

                    if (linkTextSpan) {
                         const linkText = linkTextSpan.textContent;
                         headerTitle.textContent = linkText;
                    }


                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    e.currentTarget.classList.add('active');


                    contentSections.forEach(section => section.classList.remove('active'));
                    if(targetContent) {
                        targetContent.classList.add('active');
                    }

                    if (window.innerWidth < 1024 && sidebar.classList.contains('open')) {
                        toggleMobileSidebar();
                    }
                });
            });

            // --- Task Board Logic ---
            const addTaskBtn = document.getElementById('add-task-btn');
            const addTaskModal = document.getElementById('add-task-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const cancelModalBtn = document.getElementById('cancel-modal-btn');
            const addTaskForm = document.getElementById('add-task-form');
            const allTasksContainer = document.getElementById('all-tasks');
            
            const openTaskModal = () => addTaskModal.classList.remove('hidden');
            const closeTaskModal = () => addTaskModal.classList.add('hidden');

            if (addTaskBtn) addTaskBtn.addEventListener('click', openTaskModal);
            if (closeModalBtn) closeModalBtn.addEventListener('click', closeTaskModal);
            if (cancelModalBtn) cancelModalBtn.addEventListener('click', closeTaskModal);

            if (addTaskForm) {
                addTaskForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const title = e.target.title.value;
                    const description = e.target.description.value;
                    const category = e.target.category.value;
                    
                    if (!title || !category) return;

                    const newTaskCard = document.createElement('div');
                    newTaskCard.className = 'task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm';
                    newTaskCard.setAttribute('draggable', 'true');
                    newTaskCard.innerHTML = `
                        <div class="flex items-center gap-4">
                            <div class="flex items-center gap-3 flex-shrink-0">
                                <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h5 class="font-semibold text-slate-900 mb-1 truncate">${title}</h5>
                                <p class="text-sm text-slate-600 line-clamp-1">${description}</p>
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-md border border-blue-200">${category}</span>
                            </div>
                        </div>
                    `;
                    
                    const addTaskButton = allTasksContainer.querySelector('button');
                    allTasksContainer.insertBefore(newTaskCard, addTaskButton);

                    e.target.reset();
                    closeTaskModal();
                });
            }

            // --- Initializations ---
            initSidebar();
            window.addEventListener('resize', initSidebar);
        });
    </script>
</body>
</html>